import { NextRequest, NextResponse } from 'next/server'
import { hashPassword } from '@/lib/auth-utils'
import { createUser, getUserByEmail, prisma } from '@/lib/db'
import { validateEmail, validatePassword } from '@/lib/utils'
import { generateOTP, createVerificationToken, sendOTPEmail } from '@/lib/otp'

export async function POST(request: NextRequest) {
  try {
    const { name, email, password } = await request.json()

    // Validate input
    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      )
    }

    if (!validateEmail(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      )
    }

    const passwordValidation = validatePassword(password)
    if (!passwordValidation.isValid) {
      return NextResponse.json(
        { error: 'Password validation failed', details: passwordValidation.errors },
        { status: 400 }
      )
    }

    // Check if user already exists
    const existingUser = await getUserByEmail(email)
    if (existingUser) {
      // If user exists but email is not verified, update password and prompt OTP
      if (!existingUser.emailVerified) {
        console.log(`🔄 User exists but not verified, updating password and prompting OTP: ${email}`)

        // Hash the new password and update the user
        const hashedPassword = await hashPassword(password)
        await prisma.user.update({
          where: { id: existingUser.id },
          data: {
            name: name || existingUser.name, // Update name if provided
            password: hashedPassword, // Update with new password
          }
        })

        // Generate new OTP and send email
        const otp = generateOTP()
        await createVerificationToken(existingUser.id, otp, 'email_verification', 15)
        await sendOTPEmail(email, name || existingUser.name || 'User', otp, 'verification')

        return NextResponse.json(
          {
            message: 'We found your account. Your password has been updated. Please check your email and enter the 6-digit verification code to complete your registration.',
            requiresVerification: true,
            userId: existingUser.id
          },
          { status: 200 }
        )
      }

      // User exists and is verified
      console.log(`❌ Signup attempt with existing verified email: ${email}`)
      return NextResponse.json(
        {
          error: 'This email is already registered. Please use the "Sign In" button to access your existing account.',
          code: 'EMAIL_EXISTS'
        },
        { status: 409 }
      )
    }

    // Hash password and create unverified user
    const hashedPassword = await hashPassword(password)
    const user = await createUser({
      name,
      email,
      password: hashedPassword,
      verified: false, // User starts unverified
    })

    // Generate OTP and send verification email
    const otp = generateOTP()
    await createVerificationToken(user.id, otp, 'email_verification', 15)
    await sendOTPEmail(email, name || 'User', otp, 'verification')

    console.log(`✅ User created and verification email sent: ${email}`)

    return NextResponse.json(
      {
        message: 'Account created! Please check your email and enter the 6-digit verification code.',
        requiresVerification: true,
        userId: user.id
      },
      { status: 201 }
    )
  } catch (error) {
    console.error('Signup error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
